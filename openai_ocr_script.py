#!/usr/bin/env python3
"""
简单的OCR图片识别脚本
使用vLLM服务器，兼容OpenAI API格式
"""

import os
import base64
import json
import argparse
import sys
from typing import Optional, Dict, Any
from pathlib import Path
from PIL import Image
import requests
from io import BytesIO


class SimpleOCRClient:
    """简单的OCR客户端，兼容OpenAI API"""

    def __init__(self,
                 base_url: str = "http://localhost",
                 port: int = 8002,
                 model: str = "models/",
                 api_key: Optional[str] = None):
        """
        初始化OCR客户端

        Args:
            base_url: vLLM服务器基础URL
            port: vLLM服务器端口
            model: 模型名称
            api_key: API密钥（可选）
        """
        self.base_url = base_url
        self.port = port
        self.model = model
        self.api_key = api_key
        self.completion_url = f"{base_url}:{port}/v1/chat/completions"

        # 设置请求头
        self.headers = {
            "Content-Type": "application/json"
        }
        if api_key:
            self.headers["Authorization"] = f"Bearer {api_key}"

    def encode_image(self, image_path: str) -> str:
        """
        将图片编码为base64字符串

        Args:
            image_path: 图片文件路径

        Returns:
            base64编码的图片字符串
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"图片文件不存在: {image_path}")

            # 使用PIL打开图片
            with Image.open(image_path) as img:
                # 转换为RGB模式（如果需要）
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 保存到BytesIO对象
                buffered = BytesIO()
                img.save(buffered, format="PNG")

                # 编码为base64
                img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
                return img_base64

        except Exception as e:
            raise ValueError(f"图片编码失败: {str(e)}")

    def create_prompt(self, custom_prompt: Optional[str] = None) -> str:
        """
        创建识别提示词

        Args:
            custom_prompt: 自定义提示词

        Returns:
            最终的提示词
        """
        if custom_prompt:
            return custom_prompt

        # 默认提示词
        return "请分析这张图片并提取其中的所有文本内容，保持原有的格式和结构。"

    def recognize_image(self,
                       image_path: str,
                       custom_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        识别图片中的文本

        Args:
            image_path: 图片文件路径
            custom_prompt: 自定义提示词

        Returns:
            包含识别结果的字典
        """
        try:
            # 编码图片
            base64_image = self.encode_image(image_path)

            # 创建提示词
            prompt = self.create_prompt(custom_prompt)

            # 构建请求数据（兼容OpenAI API格式）
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 4000,
                "temperature": 0.0
            }

            # 发送请求
            response = requests.post(
                self.completion_url,
                headers=self.headers,
                json=payload,
                timeout=60
            )

            # 检查响应状态
            if response.status_code != 200:
                raise Exception(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")

            # 解析响应
            result = response.json()

            if 'choices' not in result or len(result['choices']) == 0:
                raise Exception("API响应格式错误，未找到choices字段")

            # 提取识别结果
            recognized_text = result['choices'][0]['message']['content']

            return {
                "success": True,
                "text": recognized_text,
                "model": self.model,
                "usage": result.get('usage', {}),
                "image_path": image_path
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "image_path": image_path
            }

    def batch_recognize(self, image_paths: list, custom_prompt: Optional[str] = None) -> list:
        """
        批量识别多张图片

        Args:
            image_paths: 图片文件路径列表
            custom_prompt: 自定义提示词

        Returns:
            识别结果列表
        """
        results = []
        for i, image_path in enumerate(image_paths):
            print(f"正在处理第 {i+1}/{len(image_paths)} 张图片: {image_path}")
            result = self.recognize_image(image_path, custom_prompt)
            results.append(result)
        return results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="使用OCRFlux模型识别图片中的文本")
    parser.add_argument("image_path", help="图片文件路径或包含图片的目录")
    parser.add_argument("--model-path", default="ChatDOC/OCRFlux-3B", help="OCRFlux模型路径")
    parser.add_argument("--base-url", default="http://localhost", help="vLLM服务器基础URL")
    parser.add_argument("--port", type=int, default=30024, help="vLLM服务器端口")
    parser.add_argument("--api-key", help="API密钥（可选）")
    parser.add_argument("--prompt", help="自定义提示词")
    parser.add_argument("--max-retries", type=int, default=3, help="最大重试次数")
    parser.add_argument("--target-image-dim", type=int, default=1024, help="图片最长边尺寸")
    parser.add_argument("--output", help="输出文件路径（可选）")
    parser.add_argument("--batch", action="store_true", help="批量处理目录中的所有图片")

    args = parser.parse_args()

    try:
        # 初始化OCR客户端
        ocr = SimpleOCRClient(
            base_url=args.base_url,
            port=args.port,
            model=args.model_path,
            api_key=args.api_key
        )

        # 确定要处理的图片路径
        image_paths = []
        if args.batch and os.path.isdir(args.image_path):
            # 批量处理目录中的图片
            supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp', '.pdf'}
            for file_path in Path(args.image_path).rglob('*'):
                if file_path.suffix.lower() in supported_formats:
                    image_paths.append(str(file_path))

            if not image_paths:
                print(f"在目录 {args.image_path} 中未找到支持的图片文件")
                return

            print(f"找到 {len(image_paths)} 个文件")

        elif os.path.isfile(args.image_path):
            # 处理单张图片
            image_paths = [args.image_path]
        else:
            print(f"错误: 路径 {args.image_path} 不存在")
            return

        # 执行识别
        print(f"开始处理，使用模型: {args.model_path}")
        print(f"服务器地址: {args.base_url}:{args.port}")

        if len(image_paths) == 1:
            print(f"正在处理图片: {image_paths[0]}")
            result = ocr.recognize_image(
                image_paths[0],
                args.prompt,
                args.max_retries,
                args.target_image_dim
            )
            results = [result]
        else:
            print(f"正在批量处理 {len(image_paths)} 个文件...")
            results = ocr.batch_recognize(
                image_paths,
                args.prompt,
                args.max_retries,
                args.target_image_dim
            )

        # 输出结果
        output_content = []
        success_count = 0

        for result in results:
            if result['success']:
                success_count += 1
                print(f"\n{'='*50}")
                print(f"文件: {result['image_path']}")
                print(f"模型: {result['model']}")
                if 'usage' in result and result['usage']:
                    print(f"Token使用: {result['usage']}")
                print(f"{'='*50}")
                print(result['text'])

                output_content.append(f"文件: {result['image_path']}\n")
                output_content.append(f"识别结果:\n{result['text']}\n\n")
            else:
                print(f"\n❌ 错误: 处理文件 {result['image_path']} 失败")
                print(f"错误信息: {result['error']}")

        print(f"\n处理完成: {success_count}/{len(results)} 个文件成功")

        # 保存到文件（如果指定了输出路径）
        if args.output and output_content:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.writelines(output_content)
            print(f"结果已保存到: {args.output}")

    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
