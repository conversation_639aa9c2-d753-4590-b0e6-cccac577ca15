#!/usr/bin/env python3
"""
OCRFlux图片识别脚本
基于vLLM和OCRFlux模型，兼容OpenAI API格式
支持自定义提示词的OCR功能
"""

import os
import base64
import json
import argparse
import sys
import asyncio
from typing import Optional, Dict, Any, List
from pathlib import Path
from PIL import Image
import requests
from io import BytesIO
from urllib.parse import urlparse

# OCRFlux相关导入
from ocrflux.image_utils import get_page_image, is_image
from ocrflux.prompts import PageResponse, build_page_to_markdown_prompt
from ocrflux.table_format import table_matrix2html


class OCRFluxClient:
    """使用OCRFlux模型进行图片识别的客户端类"""

    def __init__(self,
                 model_path: str = "ChatDOC/OCRFlux-3B",
                 base_url: str = "http://localhost",
                 port: int = 30024,
                 api_key: Optional[str] = None):
        """
        初始化OCRFlux客户端

        Args:
            model_path: OCRFlux模型路径
            base_url: vLLM服务器基础URL
            port: vLLM服务器端口
            api_key: API密钥（可选，用于兼容性）
        """
        self.model_path = model_path
        self.base_url = base_url
        self.port = port
        self.api_key = api_key
        self.completion_url = f"{base_url}:{port}/v1/chat/completions"

        # 设置请求头
        self.headers = {
            "Content-Type": "application/json"
        }
        if api_key:
            self.headers["Authorization"] = f"Bearer {api_key}"

    def encode_image(self, image_path: str, target_longest_image_dim: int = 1024) -> str:
        """
        使用OCRFlux的图片处理方式编码图片

        Args:
            image_path: 图片文件路径
            target_longest_image_dim: 图片最长边的目标尺寸

        Returns:
            base64编码的图片字符串
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"图片文件不存在: {image_path}")

            # 使用OCRFlux的图片处理函数
            if image_path.lower().endswith('.pdf'):
                # 如果是PDF，处理第一页
                image = get_page_image(image_path, 1, target_longest_image_dim=target_longest_image_dim)
            else:
                # 如果是图片文件
                image = get_page_image(image_path, 1, target_longest_image_dim=target_longest_image_dim)

            # 编码为base64
            buffered = BytesIO()
            image.save(buffered, format="PNG")
            img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
            return img_base64

        except Exception as e:
            raise ValueError(f"图片编码失败: {str(e)}")

    def create_prompt(self, custom_prompt: Optional[str] = None) -> str:
        """
        创建识别提示词

        Args:
            custom_prompt: 自定义提示词

        Returns:
            最终的提示词
        """
        if custom_prompt:
            return custom_prompt

        # 使用OCRFlux的默认提示词
        return build_page_to_markdown_prompt()

    async def apost(self, url: str, json_data: dict) -> tuple:
        """
        异步HTTP POST请求（基于OCRFlux的实现）
        """
        parsed_url = urlparse(url)
        host = parsed_url.hostname
        port = parsed_url.port or 80
        path = parsed_url.path or "/"

        writer = None
        try:
            reader, writer = await asyncio.open_connection(host, port)

            json_payload = json.dumps(json_data)
            request = (
                f"POST {path} HTTP/1.1\r\n"
                f"Host: {host}\r\n"
                f"Content-Type: application/json\r\n"
                f"Content-Length: {len(json_payload)}\r\n"
                f"Connection: close\r\n\r\n"
                f"{json_payload}"
            )
            writer.write(request.encode())
            await writer.drain()

            # 读取状态行
            status_line = await reader.readline()
            if not status_line:
                raise ConnectionError("服务器无响应")
            status_parts = status_line.decode().strip().split(" ", 2)
            if len(status_parts) < 2:
                raise ValueError(f"状态行格式错误: {status_line.decode().strip()}")
            status_code = int(status_parts[1])

            # 读取响应头
            headers = {}
            while True:
                line = await reader.readline()
                if line in (b"\r\n", b"\n", b""):
                    break
                key, _, value = line.decode().partition(":")
                headers[key.strip().lower()] = value.strip()

            # 读取响应体
            if "content-length" in headers:
                body_length = int(headers["content-length"])
                response_body = await reader.readexactly(body_length)
            else:
                raise ConnectionError("不支持非固定长度的响应")

            return status_code, response_body
        except Exception as e:
            raise e
        finally:
            if writer is not None:
                try:
                    writer.close()
                    await writer.wait_closed()
                except:
                    pass

    def build_query(self, image_path: str, custom_prompt: Optional[str] = None,
                   target_longest_image_dim: int = 1024) -> dict:
        """
        构建OCRFlux格式的查询请求
        """
        # 编码图片
        base64_image = self.encode_image(image_path, target_longest_image_dim)

        # 创建提示词
        prompt = self.create_prompt(custom_prompt)

        return {
            "model": self.model_path,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}"}},
                    ],
                }
            ],
            "temperature": 0.0,
        }

    async def recognize_image_async(self,
                                   image_path: str,
                                   custom_prompt: Optional[str] = None,
                                   max_retries: int = 3,
                                   target_longest_image_dim: int = 1024) -> Dict[str, Any]:
        """
        异步识别图片中的文本

        Args:
            image_path: 图片文件路径
            custom_prompt: 自定义提示词
            max_retries: 最大重试次数
            target_longest_image_dim: 图片最长边尺寸

        Returns:
            包含识别结果的字典
        """
        attempt = 0
        while attempt < max_retries:
            try:
                # 构建查询
                query = self.build_query(image_path, custom_prompt, target_longest_image_dim)
                query["temperature"] = 0.1 * attempt

                # 发送异步请求
                status_code, response_body = await self.apost(self.completion_url, query)

                if status_code != 200:
                    raise ValueError(f"HTTP状态错误: {status_code}")

                # 解析响应
                result = json.loads(response_body)
                response_content = result["choices"][0]["message"]["content"]

                # 处理OCRFlux格式的响应
                try:
                    # 尝试解析为JSON（OCRFlux格式）
                    model_response_json = json.loads(response_content)
                    page_response = PageResponse(**model_response_json)
                    natural_text = page_response.natural_text

                    # 处理表格格式
                    markdown_elements = []
                    for text in natural_text.split('\n\n'):
                        if text.startswith("<Image>") and text.endswith("</Image>"):
                            # 跳过图像标记
                            pass
                        elif text.startswith("<table>") and text.endswith("</table>"):
                            try:
                                # 转换表格格式
                                new_text = table_matrix2html(text)
                            except:
                                new_text = text.replace("<t>","").replace("<l>","").replace("<lt>","")
                            markdown_elements.append(new_text)
                        else:
                            markdown_elements.append(text)

                    processed_text = '\n\n'.join(markdown_elements)

                except (json.JSONDecodeError, TypeError):
                    # 如果不是JSON格式，直接使用原始响应
                    processed_text = response_content

                return {
                    "success": True,
                    "text": processed_text,
                    "model": self.model_path,
                    "usage": result.get('usage', {}),
                    "image_path": image_path,
                    "raw_response": response_content
                }

            except Exception as e:
                attempt += 1
                if attempt >= max_retries:
                    return {
                        "success": False,
                        "error": str(e),
                        "image_path": image_path
                    }

        return {
            "success": False,
            "error": "达到最大重试次数",
            "image_path": image_path
        }

    def recognize_image(self,
                       image_path: str,
                       custom_prompt: Optional[str] = None,
                       max_retries: int = 3,
                       target_longest_image_dim: int = 1024) -> Dict[str, Any]:
        """
        同步识别图片中的文本
        """
        return asyncio.run(self.recognize_image_async(
            image_path, custom_prompt, max_retries, target_longest_image_dim
        ))

    async def batch_recognize_async(self,
                                   image_paths: List[str],
                                   custom_prompt: Optional[str] = None,
                                   max_retries: int = 3,
                                   target_longest_image_dim: int = 1024) -> List[Dict[str, Any]]:
        """
        异步批量识别多张图片
        """
        tasks = []
        for image_path in image_paths:
            task = self.recognize_image_async(
                image_path, custom_prompt, max_retries, target_longest_image_dim
            )
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "success": False,
                    "error": str(result),
                    "image_path": image_paths[i]
                })
            else:
                processed_results.append(result)

        return processed_results

    def batch_recognize(self,
                       image_paths: List[str],
                       custom_prompt: Optional[str] = None,
                       max_retries: int = 3,
                       target_longest_image_dim: int = 1024) -> List[Dict[str, Any]]:
        """
        同步批量识别多张图片
        """
        return asyncio.run(self.batch_recognize_async(
            image_paths, custom_prompt, max_retries, target_longest_image_dim
        ))


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="使用OCRFlux模型识别图片中的文本")
    parser.add_argument("image_path", help="图片文件路径或包含图片的目录")
    parser.add_argument("--model-path", default="ChatDOC/OCRFlux-3B", help="OCRFlux模型路径")
    parser.add_argument("--base-url", default="http://localhost", help="vLLM服务器基础URL")
    parser.add_argument("--port", type=int, default=30024, help="vLLM服务器端口")
    parser.add_argument("--api-key", help="API密钥（可选）")
    parser.add_argument("--prompt", help="自定义提示词")
    parser.add_argument("--max-retries", type=int, default=3, help="最大重试次数")
    parser.add_argument("--target-image-dim", type=int, default=1024, help="图片最长边尺寸")
    parser.add_argument("--output", help="输出文件路径（可选）")
    parser.add_argument("--batch", action="store_true", help="批量处理目录中的所有图片")

    args = parser.parse_args()

    try:
        # 初始化OCRFlux客户端
        ocr = OCRFluxClient(
            model_path=args.model_path,
            base_url=args.base_url,
            port=args.port,
            api_key=args.api_key
        )

        # 确定要处理的图片路径
        image_paths = []
        if args.batch and os.path.isdir(args.image_path):
            # 批量处理目录中的图片
            supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp', '.pdf'}
            for file_path in Path(args.image_path).rglob('*'):
                if file_path.suffix.lower() in supported_formats:
                    image_paths.append(str(file_path))

            if not image_paths:
                print(f"在目录 {args.image_path} 中未找到支持的图片文件")
                return

            print(f"找到 {len(image_paths)} 个文件")

        elif os.path.isfile(args.image_path):
            # 处理单张图片
            image_paths = [args.image_path]
        else:
            print(f"错误: 路径 {args.image_path} 不存在")
            return

        # 执行识别
        print(f"开始处理，使用模型: {args.model_path}")
        print(f"服务器地址: {args.base_url}:{args.port}")

        if len(image_paths) == 1:
            print(f"正在处理图片: {image_paths[0]}")
            result = ocr.recognize_image(
                image_paths[0],
                args.prompt,
                args.max_retries,
                args.target_image_dim
            )
            results = [result]
        else:
            print(f"正在批量处理 {len(image_paths)} 个文件...")
            results = ocr.batch_recognize(
                image_paths,
                args.prompt,
                args.max_retries,
                args.target_image_dim
            )

        # 输出结果
        output_content = []
        success_count = 0

        for result in results:
            if result['success']:
                success_count += 1
                print(f"\n{'='*50}")
                print(f"文件: {result['image_path']}")
                print(f"模型: {result['model']}")
                if 'usage' in result and result['usage']:
                    print(f"Token使用: {result['usage']}")
                print(f"{'='*50}")
                print(result['text'])

                output_content.append(f"文件: {result['image_path']}\n")
                output_content.append(f"识别结果:\n{result['text']}\n\n")
            else:
                print(f"\n❌ 错误: 处理文件 {result['image_path']} 失败")
                print(f"错误信息: {result['error']}")

        print(f"\n处理完成: {success_count}/{len(results)} 个文件成功")

        # 保存到文件（如果指定了输出路径）
        if args.output and output_content:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.writelines(output_content)
            print(f"结果已保存到: {args.output}")

    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
