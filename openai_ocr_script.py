#!/usr/bin/env python3
"""
OpenAI API图片识别脚本
支持自定义提示词的OCR功能
"""

import os
import base64
import json
import argparse
import sys
from typing import Optional, Dict, Any
from pathlib import Path
from PIL import Image
import requests
from io import BytesIO


class OpenAIOCR:
    """使用OpenAI API进行图片识别的类"""
    
    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        """
        初始化OpenAI OCR客户端
        
        Args:
            api_key: OpenAI API密钥，如果为None则从环境变量OPENAI_API_KEY获取
            base_url: API基础URL，默认为OpenAI官方API
        """
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("API密钥未提供。请设置OPENAI_API_KEY环境变量或传入api_key参数")
        
        self.base_url = base_url or "https://api.openai.com/v1"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def encode_image(self, image_path: str) -> str:
        """
        将图片编码为base64字符串
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            base64编码的图片字符串
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"图片文件不存在: {image_path}")
            
            # 使用PIL打开图片以验证格式
            with Image.open(image_path) as img:
                # 转换为RGB模式（如果需要）
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 保存到BytesIO对象
                buffered = BytesIO()
                img.save(buffered, format="PNG")
                
                # 编码为base64
                img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
                return img_base64
                
        except Exception as e:
            raise ValueError(f"图片编码失败: {str(e)}")
    
    def create_prompt(self, custom_prompt: Optional[str] = None) -> str:
        """
        创建识别提示词
        
        Args:
            custom_prompt: 自定义提示词
            
        Returns:
            最终的提示词
        """
        if custom_prompt:
            return custom_prompt
        
        # 默认提示词（基于OCRFlux的提示词优化）
        default_prompt = (
            "请分析这张图片并提取其中的文本内容。要求：\n"
            "1. 按照自然阅读顺序提取文本\n"
            "2. 如果有表格，请用HTML格式表示\n"
            "3. 保持原有的段落结构和格式\n"
            "4. 如果有标题，请用Markdown格式标记\n"
            "5. 不要添加任何不存在的内容\n"
            "6. 如果图片中有图表或图像，请简要描述其位置和内容\n"
            "请直接返回提取的文本内容："
        )
        return default_prompt
    
    def recognize_image(self, 
                       image_path: str, 
                       custom_prompt: Optional[str] = None,
                       model: str = "gpt-4o",
                       max_tokens: int = 4000,
                       temperature: float = 0.0) -> Dict[str, Any]:
        """
        识别图片中的文本
        
        Args:
            image_path: 图片文件路径
            custom_prompt: 自定义提示词
            model: 使用的模型名称
            max_tokens: 最大token数
            temperature: 温度参数
            
        Returns:
            包含识别结果的字典
        """
        try:
            # 编码图片
            base64_image = self.encode_image(image_path)
            
            # 创建提示词
            prompt = self.create_prompt(custom_prompt)
            
            # 构建请求数据
            payload = {
                "model": model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": max_tokens,
                "temperature": temperature
            }
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=60
            )
            
            # 检查响应状态
            if response.status_code != 200:
                raise Exception(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
            
            # 解析响应
            result = response.json()
            
            if 'choices' not in result or len(result['choices']) == 0:
                raise Exception("API响应格式错误，未找到choices字段")
            
            # 提取识别结果
            recognized_text = result['choices'][0]['message']['content']
            
            return {
                "success": True,
                "text": recognized_text,
                "model": model,
                "usage": result.get('usage', {}),
                "image_path": image_path
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "image_path": image_path
            }
    
    def batch_recognize(self, 
                       image_paths: list, 
                       custom_prompt: Optional[str] = None,
                       model: str = "gpt-4o",
                       max_tokens: int = 4000,
                       temperature: float = 0.0) -> list:
        """
        批量识别多张图片
        
        Args:
            image_paths: 图片文件路径列表
            custom_prompt: 自定义提示词
            model: 使用的模型名称
            max_tokens: 最大token数
            temperature: 温度参数
            
        Returns:
            识别结果列表
        """
        results = []
        for i, image_path in enumerate(image_paths):
            print(f"正在处理第 {i+1}/{len(image_paths)} 张图片: {image_path}")
            result = self.recognize_image(image_path, custom_prompt, model, max_tokens, temperature)
            results.append(result)
        return results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="使用OpenAI API识别图片中的文本")
    parser.add_argument("image_path", help="图片文件路径或包含图片的目录")
    parser.add_argument("--api-key", help="OpenAI API密钥（也可通过OPENAI_API_KEY环境变量设置）")
    parser.add_argument("--base-url", help="API基础URL（默认为OpenAI官方API）")
    parser.add_argument("--prompt", help="自定义提示词")
    parser.add_argument("--model", default="gpt-4o", help="使用的模型名称（默认: gpt-4o）")
    parser.add_argument("--max-tokens", type=int, default=4000, help="最大token数（默认: 4000）")
    parser.add_argument("--temperature", type=float, default=0.0, help="温度参数（默认: 0.0）")
    parser.add_argument("--output", help="输出文件路径（可选）")
    parser.add_argument("--batch", action="store_true", help="批量处理目录中的所有图片")
    
    args = parser.parse_args()
    
    try:
        # 初始化OCR客户端
        ocr = OpenAIOCR(api_key=args.api_key, base_url=args.base_url)
        
        # 确定要处理的图片路径
        image_paths = []
        if args.batch and os.path.isdir(args.image_path):
            # 批量处理目录中的图片
            supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp'}
            for file_path in Path(args.image_path).rglob('*'):
                if file_path.suffix.lower() in supported_formats:
                    image_paths.append(str(file_path))
            
            if not image_paths:
                print(f"在目录 {args.image_path} 中未找到支持的图片文件")
                return
                
            print(f"找到 {len(image_paths)} 张图片")
            
        elif os.path.isfile(args.image_path):
            # 处理单张图片
            image_paths = [args.image_path]
        else:
            print(f"错误: 路径 {args.image_path} 不存在")
            return
        
        # 执行识别
        if len(image_paths) == 1:
            result = ocr.recognize_image(
                image_paths[0], 
                args.prompt, 
                args.model, 
                args.max_tokens, 
                args.temperature
            )
            results = [result]
        else:
            results = ocr.batch_recognize(
                image_paths, 
                args.prompt, 
                args.model, 
                args.max_tokens, 
                args.temperature
            )
        
        # 输出结果
        output_content = []
        for result in results:
            if result['success']:
                print(f"\n{'='*50}")
                print(f"图片: {result['image_path']}")
                print(f"模型: {result['model']}")
                if 'usage' in result and result['usage']:
                    print(f"Token使用: {result['usage']}")
                print(f"{'='*50}")
                print(result['text'])
                
                output_content.append(f"图片: {result['image_path']}\n")
                output_content.append(f"识别结果:\n{result['text']}\n\n")
            else:
                print(f"\n错误: 处理图片 {result['image_path']} 失败")
                print(f"错误信息: {result['error']}")
        
        # 保存到文件（如果指定了输出路径）
        if args.output and output_content:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.writelines(output_content)
            print(f"\n结果已保存到: {args.output}")
            
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
