# OpenAI OCR脚本依赖包
# 安装命令: pip install -r requirements_openai_ocr.txt

# 核心依赖
requests>=2.28.0          # HTTP请求库，用于调用OpenAI API
Pillow>=9.0.0            # 图像处理库，用于图片格式转换和验证

# 可选依赖（用于增强功能）
pathlib2>=2.3.0         # 路径处理（Python 3.4+已内置）
argparse                 # 命令行参数解析（Python标准库）
base64                   # Base64编码（Python标准库）
json                     # JSON处理（Python标准库）
os                       # 操作系统接口（Python标准库）
sys                      # 系统相关参数和函数（Python标准库）
io                       # 输入输出处理（Python标准库）
typing                   # 类型提示（Python标准库）

# 开发和测试依赖（可选）
pytest>=7.0.0           # 测试框架
pytest-cov>=4.0.0       # 测试覆盖率
black>=22.0.0           # 代码格式化
flake8>=5.0.0           # 代码检查
mypy>=0.991             # 类型检查
