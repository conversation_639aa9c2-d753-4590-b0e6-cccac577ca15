# OCRFlux 快速开始指南

这是一个基于OCRFlux模型和vLLM的图片文字识别脚本的快速开始指南。

## 🚀 快速开始

### 1. 环境准备

确保你已经安装了OCRFlux环境：

```bash
# 创建conda环境
conda create -n ocrflux python=3.11
conda activate ocrflux

# 安装OCRFlux
git clone https://github.com/chatdoc-com/OCRFlux.git
cd OCRFlux
pip install -e . --find-links https://flashinfer.ai/whl/cu124/torch2.5/flashinfer/

# 安装系统依赖（Ubuntu/Debian）
sudo apt-get update
sudo apt-get install poppler-utils poppler-data
```

### 2. 启动vLLM服务器

```bash
# 方法1: 使用辅助脚本（推荐）
python start_ocrflux_server.py --model-path ChatDOC/OCRFlux-3B --port 30024

# 方法2: 使用OCRFlux原生脚本
bash ocrflux/server.sh ChatDOC/OCRFlux-3B 30024
```

等待服务器启动完成（通常需要几分钟下载和加载模型）。

### 3. 测试脚本功能

```bash
# 运行测试脚本
python test_openai_ocr.py
```

### 4. 基础使用

```bash
# 识别单张图片
python openai_ocr_script.py 5.jpg

# 使用自定义提示词
python openai_ocr_script.py 5.jpg --prompt "请提取这张图片中的所有文字内容"

# 批量处理
python openai_ocr_script.py ./images --batch

# 保存结果到文件
python openai_ocr_script.py 5.jpg --output result.txt
```

## 📝 示例代码

### Python编程接口

```python
from openai_ocr_script import OCRFluxClient

# 初始化客户端
ocr = OCRFluxClient(
    model_path="ChatDOC/OCRFlux-3B",
    base_url="http://localhost",
    port=30024
)

# 识别单张图片
result = ocr.recognize_image("image.jpg")

if result['success']:
    print("识别结果:", result['text'])
else:
    print("识别失败:", result['error'])
```

### 批量处理

```python
# 批量处理多张图片
image_list = ["img1.jpg", "img2.jpg", "img3.jpg"]
results = ocr.batch_recognize(image_list)

for result in results:
    if result['success']:
        print(f"✅ {result['image_path']}: 识别成功")
    else:
        print(f"❌ {result['image_path']}: {result['error']}")
```

## 🎯 专用场景示例

### 考试题目识别

```python
exam_prompt = """
请仔细分析这张考试题目图片，并按以下格式提取内容：
1. 题目编号和分值
2. 题目内容（完整题干）
3. 选项内容（如果是选择题）
4. 答案（如果图片中显示了答案）

要求：
- 保持原有的题目格式和编号
- 选择题的选项要清晰标记A、B、C、D等
- 数学公式用LaTeX格式表示
"""

result = ocr.recognize_image("exam.jpg", custom_prompt=exam_prompt)
```

### 表格提取

```python
table_prompt = """
请分析这张图片中的表格内容，并按以下要求处理：
1. 识别表格的结构（行数、列数）
2. 提取表格的标题（如果有）
3. 提取表头信息
4. 提取所有单元格的内容
5. 用HTML格式输出完整的表格

要求：
- 保持表格的原有结构
- 合并单元格要正确处理
- 数字要准确识别
"""

result = ocr.recognize_image("table.jpg", custom_prompt=table_prompt)
```

## 🔧 常见问题

### Q: 服务器启动失败怎么办？

A: 检查以下几点：
1. 确保有足够的GPU内存（建议12GB+）
2. 检查CUDA版本是否兼容
3. 确保端口没有被占用
4. 查看错误日志定位问题

### Q: 识别效果不好怎么办？

A: 尝试以下方法：
1. 调整图片尺寸参数 `--target-image-dim`
2. 使用更具体的自定义提示词
3. 增加重试次数 `--max-retries`
4. 确保图片质量清晰

### Q: 如何处理大量图片？

A: 建议：
1. 使用批量处理模式 `--batch`
2. 分批处理，避免内存溢出
3. 使用异步处理提高效率
4. 监控GPU内存使用情况

## 📚 更多资源

- [完整文档](README_openai_ocr.md)
- [示例代码](example_usage.py)
- [OCRFlux项目](https://github.com/chatdoc-com/OCRFlux)
- [vLLM文档](https://docs.vllm.ai/)

## 🆘 获取帮助

如果遇到问题：

1. 查看测试脚本输出：`python test_openai_ocr.py`
2. 检查服务器状态：`python start_ocrflux_server.py --check --port 30024`
3. 查看详细错误信息和日志
4. 参考OCRFlux官方文档

## 🎉 开始使用

现在你可以开始使用OCRFlux进行图片文字识别了！

```bash
# 运行示例
python example_usage.py

# 或者直接识别你的图片
python openai_ocr_script.py your_image.jpg
```

祝你使用愉快！🚀
