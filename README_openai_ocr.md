# OpenAI API 图片识别脚本

这是一个基于OpenAI API的图片文字识别脚本，支持自定义提示词，特别适合处理考试题目、表格、文档等各种类型的图片。

## 功能特点

- 🔍 **高精度识别**: 基于OpenAI GPT-4V模型，识别准确率高
- 📝 **自定义提示词**: 支持针对不同场景定制识别提示词
- 📊 **表格支持**: 自动识别表格并输出HTML格式
- 🌍 **多语言支持**: 支持中文、英文等多种语言混合识别
- 📁 **批量处理**: 支持批量处理多张图片
- 💾 **结果保存**: 支持将识别结果保存到文件

## 安装依赖

```bash
pip install requests pillow
```

## 配置API密钥

### 方法1: 环境变量（推荐）
```bash
export OPENAI_API_KEY="your-api-key-here"
```

### 方法2: 命令行参数
```bash
python openai_ocr_script.py --api-key "your-api-key-here" image.jpg
```

## 基础使用

### 1. 识别单张图片（默认提示词）
```bash
python openai_ocr_script.py 5.jpg
```

### 2. 使用自定义提示词
```bash
python openai_ocr_script.py 5.jpg --prompt "请提取这张考试题目中的所有文字，包括题目、选项和答案"
```

### 3. 批量处理目录中的所有图片
```bash
python openai_ocr_script.py ./images --batch
```

### 4. 保存结果到文件
```bash
python openai_ocr_script.py 5.jpg --output result.txt
```

## 高级用法

### 指定模型和参数
```bash
python openai_ocr_script.py 5.jpg \
  --model gpt-4o \
  --max-tokens 4000 \
  --temperature 0.0
```

### 使用自定义API端点
```bash
python openai_ocr_script.py 5.jpg \
  --base-url "https://your-custom-endpoint.com/v1"
```

## 自定义提示词示例

### 1. 考试题目识别
```python
exam_prompt = """
请仔细分析这张考试题目图片，并按以下格式提取内容：

1. 题目编号和分值
2. 题目内容（完整题干）
3. 选项内容（如果是选择题）
4. 答案（如果图片中显示了答案）

要求：
- 保持原有的题目格式和编号
- 选择题的选项要清晰标记A、B、C、D等
- 数学公式用LaTeX格式表示
"""
```

### 2. 表格提取
```python
table_prompt = """
请分析这张图片中的表格内容，并按以下要求处理：

1. 识别表格的结构（行数、列数）
2. 提取表格的标题（如果有）
3. 提取表头信息
4. 提取所有单元格的内容
5. 用HTML格式输出完整的表格

要求：
- 保持表格的原有结构
- 合并单元格要正确处理
- 数字要准确识别
"""
```

### 3. 文档结构分析
```python
document_prompt = """
请分析这张文档图片，并提供以下信息：

1. 文档类型（如：试卷、报告、表单、证书等）
2. 主要内容摘要
3. 文档结构分析（标题、段落、列表、表格等）
4. 关键信息提取（日期、姓名、数字、重要术语等）
5. 完整的文本内容（按阅读顺序）
"""
```

## 编程接口使用

```python
from openai_ocr_script import OpenAIOCR

# 初始化客户端
ocr = OpenAIOCR(api_key="your-api-key")

# 识别单张图片
result = ocr.recognize_image("image.jpg", custom_prompt="自定义提示词")

if result['success']:
    print("识别结果:", result['text'])
    print("使用的模型:", result['model'])
    print("Token使用情况:", result['usage'])
else:
    print("识别失败:", result['error'])

# 批量识别
image_list = ["img1.jpg", "img2.jpg", "img3.jpg"]
results = ocr.batch_recognize(image_list, custom_prompt="批量处理提示词")

for result in results:
    if result['success']:
        print(f"文件 {result['image_path']} 识别成功")
        print(result['text'])
    else:
        print(f"文件 {result['image_path']} 识别失败: {result['error']}")
```

## 支持的图片格式

- PNG
- JPEG/JPG
- BMP
- GIF
- TIFF
- WebP

## 命令行参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `image_path` | 图片文件路径或目录 | 必需 |
| `--api-key` | OpenAI API密钥 | 从环境变量获取 |
| `--base-url` | API基础URL | https://api.openai.com/v1 |
| `--prompt` | 自定义提示词 | 使用默认提示词 |
| `--model` | 使用的模型 | gpt-4o |
| `--max-tokens` | 最大token数 | 4000 |
| `--temperature` | 温度参数 | 0.0 |
| `--output` | 输出文件路径 | 控制台输出 |
| `--batch` | 批量处理模式 | False |

## 错误处理

脚本包含完善的错误处理机制：

- **API密钥错误**: 检查环境变量或命令行参数
- **图片格式错误**: 自动转换为支持的格式
- **网络错误**: 提供详细的错误信息
- **API限制**: 显示具体的限制信息

## 性能优化建议

1. **图片大小**: 建议图片大小不超过20MB
2. **批量处理**: 大量图片建议分批处理，避免API限制
3. **提示词长度**: 保持提示词简洁明确，避免过长
4. **模型选择**: 根据需求选择合适的模型（gpt-4o vs gpt-4o-mini）

## 示例运行

查看 `example_usage.py` 文件，其中包含了各种使用场景的完整示例：

```bash
python example_usage.py
```

## 注意事项

1. 确保有足够的OpenAI API额度
2. 大图片可能消耗较多tokens
3. 复杂图片可能需要调整max_tokens参数
4. 建议先用小图片测试提示词效果

## 许可证

本脚本基于Apache 2.0许可证开源。
