# OCRFlux 图片识别脚本

这是一个基于OCRFlux模型和vLLM的图片文字识别脚本，兼容OpenAI API格式，支持自定义提示词，特别适合处理考试题目、表格、文档等各种类型的图片。

## 功能特点

- 🔍 **高精度识别**: 基于OCRFlux-3B模型，专门针对OCR任务优化
- 📝 **自定义提示词**: 支持针对不同场景定制识别提示词
- 📊 **表格支持**: 自动识别表格并输出HTML格式
- 🌍 **多语言支持**: 支持中文、英文等多种语言混合识别
- 📁 **批量处理**: 支持异步批量处理多张图片
- 💾 **结果保存**: 支持将识别结果保存到文件
- 🚀 **本地部署**: 基于vLLM本地部署，数据安全可控
- ⚡ **高性能**: 支持GPU加速，处理速度快

## 安装依赖

### 1. 安装OCRFlux
```bash
# 创建conda环境
conda create -n ocrflux python=3.11
conda activate ocrflux

# 克隆OCRFlux项目
git clone https://github.com/chatdoc-com/OCRFlux.git
cd OCRFlux

# 安装OCRFlux
pip install -e . --find-links https://flashinfer.ai/whl/cu124/torch2.5/flashinfer/
```

### 2. 安装系统依赖（Ubuntu/Debian）
```bash
sudo apt-get update
sudo apt-get install poppler-utils poppler-data ttf-mscorefonts-installer msttcorefonts fonts-crosextra-caladea fonts-crosextra-carlito gsfonts lcdf-typetools
```

## 启动vLLM服务器

### 方法1: 使用辅助脚本（推荐）
```bash
# 启动服务器
python start_ocrflux_server.py --model-path ChatDOC/OCRFlux-3B --port 30024

# 后台启动
python start_ocrflux_server.py --model-path ChatDOC/OCRFlux-3B --port 30024 --background

# 检查服务器状态
python start_ocrflux_server.py --check --port 30024

# 停止服务器
python start_ocrflux_server.py --stop --port 30024
```

### 方法2: 使用OCRFlux原生脚本
```bash
bash ocrflux/server.sh ChatDOC/OCRFlux-3B 30024
```

## 基础使用

### 1. 识别单张图片（默认提示词）
```bash
python openai_ocr_script.py 5.jpg
```

### 2. 使用自定义提示词
```bash
python openai_ocr_script.py 5.jpg --prompt "请提取这张考试题目中的所有文字，包括题目、选项和答案"
```

### 3. 批量处理目录中的所有图片
```bash
python openai_ocr_script.py ./images --batch
```

### 4. 保存结果到文件
```bash
python openai_ocr_script.py 5.jpg --output result.txt
```

## 高级用法

### 指定模型路径和服务器参数
```bash
python openai_ocr_script.py 5.jpg \
  --model-path /path/to/OCRFlux-3B \
  --base-url http://localhost \
  --port 30024
```

### 调整图片处理参数
```bash
python openai_ocr_script.py 5.jpg \
  --target-image-dim 2048 \
  --max-retries 5
```

## 自定义提示词示例

### 1. 考试题目识别
```python
exam_prompt = """
请仔细分析这张考试题目图片，并按以下格式提取内容：

1. 题目编号和分值
2. 题目内容（完整题干）
3. 选项内容（如果是选择题）
4. 答案（如果图片中显示了答案）

要求：
- 保持原有的题目格式和编号
- 选择题的选项要清晰标记A、B、C、D等
- 数学公式用LaTeX格式表示
"""
```

### 2. 表格提取
```python
table_prompt = """
请分析这张图片中的表格内容，并按以下要求处理：

1. 识别表格的结构（行数、列数）
2. 提取表格的标题（如果有）
3. 提取表头信息
4. 提取所有单元格的内容
5. 用HTML格式输出完整的表格

要求：
- 保持表格的原有结构
- 合并单元格要正确处理
- 数字要准确识别
"""
```

### 3. 文档结构分析
```python
document_prompt = """
请分析这张文档图片，并提供以下信息：

1. 文档类型（如：试卷、报告、表单、证书等）
2. 主要内容摘要
3. 文档结构分析（标题、段落、列表、表格等）
4. 关键信息提取（日期、姓名、数字、重要术语等）
5. 完整的文本内容（按阅读顺序）
"""
```

## 编程接口使用

```python
from openai_ocr_script import OCRFluxClient

# 初始化客户端
ocr = OCRFluxClient(
    model_path="ChatDOC/OCRFlux-3B",
    base_url="http://localhost",
    port=30024
)

# 识别单张图片
result = ocr.recognize_image("image.jpg", custom_prompt="自定义提示词")

if result['success']:
    print("识别结果:", result['text'])
    print("使用的模型:", result['model'])
    print("Token使用情况:", result['usage'])
else:
    print("识别失败:", result['error'])

# 批量识别
image_list = ["img1.jpg", "img2.jpg", "img3.jpg"]
results = ocr.batch_recognize(image_list, custom_prompt="批量处理提示词")

for result in results:
    if result['success']:
        print(f"文件 {result['image_path']} 识别成功")
        print(result['text'])
    else:
        print(f"文件 {result['image_path']} 识别失败: {result['error']}")

# 异步批量处理（更高效）
import asyncio

async def async_batch_process():
    results = await ocr.batch_recognize_async(image_list)
    return results

# 运行异步处理
results = asyncio.run(async_batch_process())
```

## 支持的文件格式

- PNG
- JPEG/JPG
- BMP
- GIF
- TIFF
- WebP
- PDF（处理第一页）

## 命令行参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `image_path` | 图片文件路径或目录 | 必需 |
| `--model-path` | OCRFlux模型路径 | ChatDOC/OCRFlux-3B |
| `--base-url` | vLLM服务器基础URL | http://localhost |
| `--port` | vLLM服务器端口 | 30024 |
| `--api-key` | API密钥（可选） | None |
| `--prompt` | 自定义提示词 | 使用OCRFlux默认提示词 |
| `--max-retries` | 最大重试次数 | 3 |
| `--target-image-dim` | 图片最长边尺寸 | 1024 |
| `--output` | 输出文件路径 | 控制台输出 |
| `--batch` | 批量处理模式 | False |

## 错误处理

脚本包含完善的错误处理机制：

- **服务器连接错误**: 检查vLLM服务器是否正在运行
- **图片格式错误**: 自动转换为支持的格式
- **模型加载错误**: 检查模型路径和GPU内存
- **网络错误**: 提供详细的错误信息和重试机制

## 性能优化建议

1. **GPU内存**: 确保有足够的GPU内存加载模型（建议12GB+）
2. **图片尺寸**: 调整target-image-dim参数平衡质量和速度
3. **批量处理**: 使用异步批量处理提高效率
4. **服务器配置**: 根据硬件调整vLLM服务器参数

## 示例运行

查看 `example_usage.py` 文件，其中包含了各种使用场景的完整示例：

```bash
# 确保vLLM服务器正在运行
python start_ocrflux_server.py --model-path ChatDOC/OCRFlux-3B --port 30024 --background

# 运行示例
python example_usage.py
```

## 注意事项

1. 确保vLLM服务器正在运行且可访问
2. 大图片可能需要更多GPU内存和处理时间
3. 复杂图片可能需要调整重试次数
4. 建议先用小图片测试提示词效果
5. OCRFlux模型专门针对OCR任务优化，效果优于通用模型

## 许可证

本脚本基于Apache 2.0许可证开源。
