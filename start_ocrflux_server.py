#!/usr/bin/env python3
"""
启动OCRFlux vLLM服务器的辅助脚本
"""

import os
import sys
import time
import subprocess
import argparse
import requests
from pathlib import Path


def check_server_status(base_url: str, port: int, timeout: int = 5) -> bool:
    """
    检查vLLM服务器是否正在运行
    
    Args:
        base_url: 服务器基础URL
        port: 服务器端口
        timeout: 超时时间
        
    Returns:
        服务器是否可用
    """
    try:
        response = requests.get(f"{base_url}:{port}/health", timeout=timeout)
        return response.status_code == 200
    except:
        return False


def wait_for_server(base_url: str, port: int, max_wait: int = 300) -> bool:
    """
    等待服务器启动
    
    Args:
        base_url: 服务器基础URL
        port: 服务器端口
        max_wait: 最大等待时间（秒）
        
    Returns:
        服务器是否成功启动
    """
    print(f"等待服务器启动 {base_url}:{port} ...")
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        if check_server_status(base_url, port):
            print("✅ 服务器启动成功！")
            return True
        
        print(".", end="", flush=True)
        time.sleep(2)
    
    print(f"\n❌ 服务器启动超时（{max_wait}秒）")
    return False


def start_vllm_server(model_path: str, port: int, gpu_memory_utilization: float = 0.916, 
                     max_model_len: int = 8192, background: bool = False) -> subprocess.Popen:
    """
    启动vLLM服务器
    
    Args:
        model_path: 模型路径
        port: 服务器端口
        gpu_memory_utilization: GPU内存使用率
        max_model_len: 最大模型长度
        background: 是否在后台运行
        
    Returns:
        subprocess.Popen对象
    """
    cmd = [
        "vllm", "serve", model_path,
        "--port", str(port),
        "--max-model-len", str(max_model_len),
        "--gpu_memory_utilization", str(gpu_memory_utilization)
    ]
    
    print(f"启动vLLM服务器...")
    print(f"模型路径: {model_path}")
    print(f"端口: {port}")
    print(f"GPU内存使用率: {gpu_memory_utilization}")
    print(f"最大模型长度: {max_model_len}")
    print(f"命令: {' '.join(cmd)}")
    
    if background:
        # 后台运行
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
    else:
        # 前台运行
        process = subprocess.Popen(cmd)
    
    return process


def stop_server_on_port(port: int):
    """
    停止指定端口上的服务器
    """
    try:
        # 查找占用端口的进程
        result = subprocess.run(
            ["lsof", "-ti", f":{port}"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0 and result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                print(f"停止进程 {pid} (端口 {port})")
                subprocess.run(["kill", "-9", pid])
            return True
        else:
            print(f"端口 {port} 上没有运行的进程")
            return False
            
    except Exception as e:
        print(f"停止服务器失败: {str(e)}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动OCRFlux vLLM服务器")
    parser.add_argument("--model-path", default="ChatDOC/OCRFlux-3B", 
                       help="OCRFlux模型路径")
    parser.add_argument("--port", type=int, default=30024, 
                       help="服务器端口")
    parser.add_argument("--gpu-memory-utilization", type=float, default=0.916,
                       help="GPU内存使用率")
    parser.add_argument("--max-model-len", type=int, default=8192,
                       help="最大模型长度")
    parser.add_argument("--background", action="store_true",
                       help="在后台运行服务器")
    parser.add_argument("--stop", action="store_true",
                       help="停止指定端口上的服务器")
    parser.add_argument("--check", action="store_true",
                       help="检查服务器状态")
    parser.add_argument("--wait", type=int, default=300,
                       help="等待服务器启动的最大时间（秒）")
    
    args = parser.parse_args()
    
    base_url = "http://localhost"
    
    try:
        if args.stop:
            # 停止服务器
            print(f"停止端口 {args.port} 上的服务器...")
            stop_server_on_port(args.port)
            return
        
        if args.check:
            # 检查服务器状态
            if check_server_status(base_url, args.port):
                print(f"✅ 服务器 {base_url}:{args.port} 正在运行")
            else:
                print(f"❌ 服务器 {base_url}:{args.port} 未运行")
            return
        
        # 检查模型路径
        if not args.model_path.startswith("ChatDOC/") and not os.path.exists(args.model_path):
            print(f"❌ 模型路径不存在: {args.model_path}")
            print("请确保模型路径正确，或使用 'ChatDOC/OCRFlux-3B' 从HuggingFace下载")
            sys.exit(1)
        
        # 检查端口是否被占用
        if check_server_status(base_url, args.port):
            print(f"⚠️  端口 {args.port} 已被占用")
            response = input("是否停止现有服务器并重新启动？(y/N): ")
            if response.lower() == 'y':
                stop_server_on_port(args.port)
                time.sleep(2)
            else:
                print("取消启动")
                return
        
        # 启动服务器
        process = start_vllm_server(
            args.model_path,
            args.port,
            args.gpu_memory_utilization,
            args.max_model_len,
            args.background
        )
        
        if args.background:
            # 后台运行，等待服务器启动
            if wait_for_server(base_url, args.port, args.wait):
                print(f"服务器已在后台启动，PID: {process.pid}")
                print(f"服务器地址: {base_url}:{args.port}")
                print(f"要停止服务器，请运行: python {sys.argv[0]} --stop --port {args.port}")
            else:
                print("服务器启动失败")
                process.terminate()
                sys.exit(1)
        else:
            # 前台运行
            print("服务器正在前台运行，按 Ctrl+C 停止")
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n正在停止服务器...")
                process.terminate()
                process.wait()
                print("服务器已停止")
    
    except Exception as e:
        print(f"启动服务器时出错: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
