#!/bin/bash

# OCRFlux GPU内存设置修改脚本
# 使用方法: ./modify_gpu_memory.sh [目标GPU内存GB] [可选:总显存GB]
# 示例: ./modify_gpu_memory.sh 11 12

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}OCRFlux GPU内存设置修改脚本${NC}"
    echo ""
    echo "使用方法:"
    echo "  $0 <目标GPU内存GB> [总显存GB]"
    echo ""
    echo "参数说明:"
    echo "  目标GPU内存GB  - 要分配的GPU内存大小(GB)"
    echo "  总显存GB      - GPU总显存大小(GB，可选，默认自动检测)"
    echo ""
    echo "示例:"
    echo "  $0 11        # 使用11GB，自动检测总显存"
    echo "  $0 11 12     # 使用11GB，总显存12GB"
    echo "  $0 20 24     # 使用20GB，总显存24GB"
    echo ""
    echo "支持的GPU配置:"
    echo "  RTX 3090: 24GB"
    echo "  RTX 4090: 24GB" 
    echo "  RTX 3080: 10/12GB"
    echo "  Tesla V100: 16/32GB"
    echo "  A100: 40/80GB"
}

# 检查参数
if [ $# -eq 0 ] || [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

TARGET_MEMORY=$1
TOTAL_MEMORY=${2:-""}

# 验证参数
if ! [[ "$TARGET_MEMORY" =~ ^[0-9]+(\.[0-9]+)?$ ]]; then
    echo -e "${RED}错误: 目标GPU内存必须是数字${NC}"
    exit 1
fi

# 自动检测GPU显存
detect_gpu_memory() {
    if command -v nvidia-smi >/dev/null 2>&1; then
        local gpu_memory=$(nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits | head -1)
        if [[ "$gpu_memory" =~ ^[0-9]+$ ]]; then
            echo $((gpu_memory / 1024))
        else
            echo ""
        fi
    else
        echo ""
    fi
}

# 计算利用率
if [ -z "$TOTAL_MEMORY" ]; then
    DETECTED_MEMORY=$(detect_gpu_memory)
    if [ -n "$DETECTED_MEMORY" ]; then
        TOTAL_MEMORY=$DETECTED_MEMORY
        echo -e "${GREEN}检测到GPU显存: ${TOTAL_MEMORY}GB${NC}"
    else
        echo -e "${YELLOW}无法自动检测GPU显存，请手动指定总显存大小${NC}"
        echo "使用方法: $0 $TARGET_MEMORY <总显存GB>"
        exit 1
    fi
fi

# 验证总显存参数
if ! [[ "$TOTAL_MEMORY" =~ ^[0-9]+(\.[0-9]+)?$ ]]; then
    echo -e "${RED}错误: 总显存必须是数字${NC}"
    exit 1
fi

# 检查内存设置合理性
if (( $(echo "$TARGET_MEMORY > $TOTAL_MEMORY" | bc -l) )); then
    echo -e "${RED}错误: 目标内存(${TARGET_MEMORY}GB)不能大于总显存(${TOTAL_MEMORY}GB)${NC}"
    exit 1
fi

if (( $(echo "$TARGET_MEMORY < 1" | bc -l) )); then
    echo -e "${RED}错误: 目标内存不能小于1GB${NC}"
    exit 1
fi

# 计算利用率
UTILIZATION=$(echo "scale=3; $TARGET_MEMORY / $TOTAL_MEMORY" | bc)

echo -e "${BLUE}GPU内存配置:${NC}"
echo "  目标内存: ${TARGET_MEMORY}GB"
echo "  总显存:   ${TOTAL_MEMORY}GB"
echo "  利用率:   ${UTILIZATION} ($(echo "$UTILIZATION * 100" | bc | cut -d. -f1)%)"

# 检查OCRFlux项目目录
if [ ! -f "ocrflux/server.sh" ] && [ ! -f "./server.sh" ]; then
    echo -e "${RED}错误: 请在OCRFlux项目根目录下运行此脚本${NC}"
    exit 1
fi

# 备份原文件
backup_dir="backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$backup_dir"

echo -e "${YELLOW}备份原文件到 $backup_dir/${NC}"

# 查找需要修改的文件
files_to_modify=()
if [ -f "ocrflux/server.sh" ]; then
    cp "ocrflux/server.sh" "$backup_dir/"
    files_to_modify+=("ocrflux/server.sh")
fi

if [ -f "ocrflux/pipeline.py" ]; then
    cp "ocrflux/pipeline.py" "$backup_dir/"
    files_to_modify+=("ocrflux/pipeline.py")
fi

if [ -f "ocrflux/inference.py" ]; then
    cp "ocrflux/inference.py" "$backup_dir/"
    files_to_modify+=("ocrflux/inference.py")
fi

if [ -f "ocrflux/check.py" ]; then
    cp "ocrflux/check.py" "$backup_dir/"
    files_to_modify+=("ocrflux/check.py")
fi

if [ ${#files_to_modify[@]} -eq 0 ]; then
    echo -e "${RED}错误: 未找到需要修改的文件${NC}"
    exit 1
fi

# 修改文件
echo -e "${YELLOW}开始修改文件...${NC}"

for file in "${files_to_modify[@]}"; do
    echo "  修改 $file"
    
    # 使用sed进行替换
    case "$file" in
        "ocrflux/server.sh")
            sed -i.bak "s/--gpu_memory_utilization [0-9.]\+/--gpu_memory_utilization $UTILIZATION/g" "$file"
            ;;
        "ocrflux/pipeline.py")
            sed -i.bak "s/str(0\.[0-9]\+)/str($UTILIZATION)/g" "$file"
            ;;
        "ocrflux/inference.py")
            sed -i.bak "s/gpu_memory_utilization=0\.[0-9]\+/gpu_memory_utilization=$UTILIZATION/g" "$file"
            ;;
        "ocrflux/check.py")
            # 将最小GPU内存要求改为目标内存
            sed -i.bak "s/min_gpu_memory: int = [0-9]\+ \* 1024\*\*3/min_gpu_memory: int = ${TARGET_MEMORY%.*} * 1024**3/g" "$file"
            ;;
    esac
    
    # 删除sed的备份文件
    rm -f "${file}.bak"
done

echo -e "${GREEN}✅ 修改完成!${NC}"
echo ""
echo -e "${BLUE}修改摘要:${NC}"
echo "  GPU利用率: $UTILIZATION"
echo "  备份目录: $backup_dir"
echo "  修改文件: ${#files_to_modify[@]} 个"

# 验证修改结果
echo ""
echo -e "${YELLOW}验证修改结果:${NC}"
for file in "${files_to_modify[@]}"; do
    case "$file" in
        "ocrflux/server.sh")
            echo -n "  $file: "
            if grep -q "gpu_memory_utilization $UTILIZATION" "$file"; then
                echo -e "${GREEN}✓${NC}"
            else
                echo -e "${RED}✗${NC}"
            fi
            ;;
        "ocrflux/pipeline.py")
            echo -n "  $file: "
            if grep -q "str($UTILIZATION)" "$file"; then
                echo -e "${GREEN}✓${NC}"
            else
                echo -e "${RED}✗${NC}"
            fi
            ;;
        "ocrflux/inference.py")
            echo -n "  $file: "
            if grep -q "gpu_memory_utilization=$UTILIZATION" "$file"; then
                echo -e "${GREEN}✓${NC}"
            else
                echo -e "${RED}✗${NC}"
            fi
            ;;
        "ocrflux/check.py")
            echo -n "  $file: "
            if grep -q "min_gpu_memory: int = ${TARGET_MEMORY%.*} \* 1024\*\*3" "$file"; then
                echo -e "${GREEN}✓${NC}"
            else
                echo -e "${RED}✗${NC}"
            fi
            ;;
    esac
done

echo ""
echo -e "${GREEN}🚀 GPU内存设置已更新为 ${TARGET_MEMORY}GB${NC}"
echo -e "${BLUE}提示: 如需恢复原设置，请从 $backup_dir 目录恢复文件${NC}"