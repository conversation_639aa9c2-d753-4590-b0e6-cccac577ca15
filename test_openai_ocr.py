#!/usr/bin/env python3
"""
测试OCRFlux脚本的功能
"""

import os
import sys
import requests
from openai_ocr_script import OCRFluxClient


def test_server_connection():
    """测试vLLM服务器连接"""
    print("=== 测试vLLM服务器连接 ===")

    try:
        response = requests.get("http://localhost:30024/health", timeout=5)
        if response.status_code == 200:
            print("✅ vLLM服务器连接成功")
            return True
        else:
            print(f"❌ vLLM服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ vLLM服务器连接失败: {str(e)}")
        print("请确保vLLM服务器正在运行:")
        print("  python start_ocrflux_server.py --model-path ChatDOC/OCRFlux-3B --port 30024")
        return False


def test_image_encoding():
    """测试图片编码功能"""
    print("\n=== 测试图片编码功能 ===")

    try:
        ocr = OCRFluxClient()

        # 测试图片编码
        if os.path.exists("5.jpg"):
            base64_image = ocr.encode_image("5.jpg")
            print(f"✅ 图片编码成功，长度: {len(base64_image)} 字符")
            print(f"前50个字符: {base64_image[:50]}...")
            return True
        else:
            print("❌ 测试图片 5.jpg 不存在")
            return False

    except Exception as e:
        print(f"❌ 图片编码测试失败: {str(e)}")
        return False


def test_prompt_creation():
    """测试提示词创建功能"""
    print("\n=== 测试提示词创建功能 ===")

    try:
        ocr = OCRFluxClient()

        # 测试默认提示词
        default_prompt = ocr.create_prompt()
        print("✅ 默认提示词:")
        print(default_prompt[:100] + "..." if len(default_prompt) > 100 else default_prompt)

        # 测试自定义提示词
        custom_prompt = "这是一个自定义的提示词"
        result_prompt = ocr.create_prompt(custom_prompt)
        print(f"\n✅ 自定义提示词: {result_prompt}")

        return True

    except Exception as e:
        print(f"❌ 提示词创建测试失败: {str(e)}")
        return False


def test_query_building():
    """测试查询构建功能"""
    print("\n=== 测试查询构建功能 ===")

    try:
        ocr = OCRFluxClient()

        if os.path.exists("5.jpg"):
            # 测试查询构建
            query = ocr.build_query("5.jpg", "测试提示词")

            # 验证查询结构
            required_fields = ["model", "messages", "temperature"]
            for field in required_fields:
                if field not in query:
                    print(f"❌ 查询缺少必需字段: {field}")
                    return False

            print("✅ 查询构建成功")
            print(f"模型: {query['model']}")
            print(f"消息数量: {len(query['messages'])}")
            print(f"温度: {query['temperature']}")
            return True
        else:
            print("❌ 测试图片 5.jpg 不存在")
            return False

    except Exception as e:
        print(f"❌ 查询构建测试失败: {str(e)}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")

    try:
        ocr = OCRFluxClient()

        # 测试不存在的图片文件
        try:
            ocr.encode_image("nonexistent.jpg")
            print("❌ 错误: 应该抛出文件不存在异常")
            return False
        except (FileNotFoundError, ValueError) as e:
            print(f"✅ 正确: 检测到文件不存在 - {str(e)}")

        return True

    except Exception as e:
        print(f"❌ 错误处理测试失败: {str(e)}")
        return False


def test_batch_functionality():
    """测试批量处理功能的结构"""
    print("\n=== 测试批量处理功能结构 ===")

    try:
        ocr = OCRFluxClient()

        # 测试空列表
        results = ocr.batch_recognize([])
        if len(results) == 0:
            print("✅ 正确: 空列表处理正常")
        else:
            print("❌ 错误: 空列表处理异常")
            return False

        print("✅ 批量处理功能结构正常")
        return True

    except Exception as e:
        print(f"❌ 批量处理功能测试失败: {str(e)}")
        return False


def test_full_recognition():
    """测试完整识别流程（需要服务器运行）"""
    print("\n=== 测试完整识别流程 ===")

    # 首先检查服务器是否运行
    if not test_server_connection():
        print("⚠️  跳过完整识别测试（服务器未运行）")
        return True

    try:
        ocr = OCRFluxClient()

        if os.path.exists("5.jpg"):
            print("正在进行完整识别测试...")
            result = ocr.recognize_image("5.jpg", "请简要描述这张图片的内容")

            if result['success']:
                print("✅ 完整识别测试成功")
                print(f"识别结果长度: {len(result['text'])} 字符")
                print(f"前100个字符: {result['text'][:100]}...")
                return True
            else:
                print(f"❌ 识别失败: {result['error']}")
                return False
        else:
            print("⚠️  测试图片 5.jpg 不存在，跳过完整识别测试")
            return True

    except Exception as e:
        print(f"❌ 完整识别测试失败: {str(e)}")
        return False


def main():
    """运行所有测试"""
    print("OCRFlux脚本功能测试")
    print("=" * 50)

    tests = [
        test_server_connection,
        test_image_encoding,
        test_prompt_creation,
        test_query_building,
        test_error_handling,
        test_batch_functionality,
        test_full_recognition
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1
        print()

    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")

    if passed == total:
        print("✅ 所有测试通过！脚本功能正常")
    elif passed >= total - 1:
        print("⚠️  大部分测试通过，脚本基本功能正常")
    else:
        print("❌ 部分测试失败，请检查相关功能")

    # 提供使用建议
    print("\n使用建议:")
    print("1. 启动vLLM服务器: python start_ocrflux_server.py --model-path ChatDOC/OCRFlux-3B --port 30024")
    print("2. 基础使用: python openai_ocr_script.py 5.jpg")
    print("3. 自定义提示词: python openai_ocr_script.py 5.jpg --prompt '自定义提示词'")
    print("4. 批量处理: python openai_ocr_script.py ./images --batch")
    print("5. 查看更多示例: python example_usage.py")


if __name__ == "__main__":
    main()
