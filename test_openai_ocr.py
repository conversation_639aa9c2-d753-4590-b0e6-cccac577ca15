#!/usr/bin/env python3
"""
测试OpenAI OCR脚本的功能
"""

import os
import sys
from openai_ocr_script import OpenAIOCR


def test_image_encoding():
    """测试图片编码功能"""
    print("=== 测试图片编码功能 ===")
    
    try:
        ocr = OpenAIOCR()
        
        # 测试图片编码
        if os.path.exists("5.jpg"):
            base64_image = ocr.encode_image("5.jpg")
            print(f"图片编码成功，长度: {len(base64_image)} 字符")
            print(f"前50个字符: {base64_image[:50]}...")
            return True
        else:
            print("测试图片 5.jpg 不存在")
            return False
            
    except Exception as e:
        print(f"图片编码测试失败: {str(e)}")
        return False


def test_prompt_creation():
    """测试提示词创建功能"""
    print("\n=== 测试提示词创建功能 ===")
    
    try:
        ocr = OpenAIOCR()
        
        # 测试默认提示词
        default_prompt = ocr.create_prompt()
        print("默认提示词:")
        print(default_prompt)
        
        # 测试自定义提示词
        custom_prompt = "这是一个自定义的提示词"
        result_prompt = ocr.create_prompt(custom_prompt)
        print(f"\n自定义提示词: {result_prompt}")
        
        return True
        
    except Exception as e:
        print(f"提示词创建测试失败: {str(e)}")
        return False


def test_api_key_validation():
    """测试API密钥验证"""
    print("\n=== 测试API密钥验证 ===")
    
    try:
        # 测试无API密钥的情况
        old_key = os.environ.get('OPENAI_API_KEY')
        if 'OPENAI_API_KEY' in os.environ:
            del os.environ['OPENAI_API_KEY']
        
        try:
            ocr = OpenAIOCR()
            print("错误: 应该抛出API密钥缺失异常")
            return False
        except ValueError as e:
            print(f"正确: 检测到API密钥缺失 - {str(e)}")
        
        # 恢复API密钥
        if old_key:
            os.environ['OPENAI_API_KEY'] = old_key
        
        # 测试有API密钥的情况
        if old_key:
            ocr = OpenAIOCR()
            print("正确: API密钥验证通过")
        else:
            print("警告: 未设置OPENAI_API_KEY环境变量")
        
        return True
        
    except Exception as e:
        print(f"API密钥验证测试失败: {str(e)}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    try:
        # 设置一个假的API密钥进行测试
        ocr = OpenAIOCR(api_key="fake-api-key")
        
        # 测试不存在的图片文件
        try:
            ocr.encode_image("nonexistent.jpg")
            print("错误: 应该抛出文件不存在异常")
            return False
        except (FileNotFoundError, ValueError) as e:
            print(f"正确: 检测到文件不存在 - {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"错误处理测试失败: {str(e)}")
        return False


def test_batch_functionality():
    """测试批量处理功能的结构"""
    print("\n=== 测试批量处理功能结构 ===")
    
    try:
        ocr = OpenAIOCR(api_key="fake-api-key")
        
        # 测试空列表
        results = ocr.batch_recognize([])
        if len(results) == 0:
            print("正确: 空列表处理正常")
        else:
            print("错误: 空列表处理异常")
            return False
        
        print("批量处理功能结构正常")
        return True
        
    except Exception as e:
        print(f"批量处理功能测试失败: {str(e)}")
        return False


def main():
    """运行所有测试"""
    print("OpenAI OCR脚本功能测试")
    print("=" * 50)
    
    tests = [
        test_image_encoding,
        test_prompt_creation,
        test_api_key_validation,
        test_error_handling,
        test_batch_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过！脚本功能正常")
    else:
        print("❌ 部分测试失败，请检查相关功能")
    
    # 提供使用建议
    print("\n使用建议:")
    print("1. 设置环境变量: export OPENAI_API_KEY='your-api-key'")
    print("2. 基础使用: python openai_ocr_script.py 5.jpg")
    print("3. 自定义提示词: python openai_ocr_script.py 5.jpg --prompt '自定义提示词'")
    print("4. 查看更多示例: python example_usage.py")


if __name__ == "__main__":
    main()
