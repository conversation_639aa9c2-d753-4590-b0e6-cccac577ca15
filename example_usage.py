#!/usr/bin/env python3
"""
OpenAI OCR脚本使用示例
演示如何使用不同的自定义提示词进行图片识别
"""

import os
from openai_ocr_script import OpenAIOCR


def example_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 初始化OCR客户端
    ocr = OpenAIOCR()
    
    # 识别单张图片（使用默认提示词）
    result = ocr.recognize_image("5.jpg")
    
    if result['success']:
        print("识别成功!")
        print(f"识别结果:\n{result['text']}")
    else:
        print(f"识别失败: {result['error']}")


def example_custom_prompts():
    """自定义提示词示例"""
    print("\n=== 自定义提示词示例 ===")
    
    ocr = OpenAIOCR()
    
    # 示例1: 专门用于试卷/考题识别的提示词
    exam_prompt = """
    请仔细分析这张考试题目图片，并按以下格式提取内容：
    
    1. 题目编号和分值
    2. 题目内容（完整题干）
    3. 选项内容（如果是选择题）
    4. 答案（如果图片中显示了答案）
    5. 解析（如果有）
    
    要求：
    - 保持原有的题目格式和编号
    - 选择题的选项要清晰标记A、B、C、D等
    - 数学公式用LaTeX格式表示
    - 图表用文字描述其内容和位置
    
    请按照上述格式整理输出：
    """
    
    result = ocr.recognize_image("5.jpg", custom_prompt=exam_prompt)
    
    if result['success']:
        print("考试题目识别结果:")
        print(result['text'])
    else:
        print(f"识别失败: {result['error']}")


def example_table_extraction():
    """表格提取示例"""
    print("\n=== 表格提取示例 ===")
    
    ocr = OpenAIOCR()
    
    # 专门用于表格提取的提示词
    table_prompt = """
    请分析这张图片中的表格内容，并按以下要求处理：
    
    1. 识别表格的结构（行数、列数）
    2. 提取表格的标题（如果有）
    3. 提取表头信息
    4. 提取所有单元格的内容
    5. 用HTML格式输出完整的表格
    
    要求：
    - 保持表格的原有结构
    - 合并单元格要正确处理
    - 数字要准确识别
    - 如果有多个表格，分别处理
    
    请直接输出HTML格式的表格：
    """
    
    result = ocr.recognize_image("5.jpg", custom_prompt=table_prompt)
    
    if result['success']:
        print("表格提取结果:")
        print(result['text'])
    else:
        print(f"识别失败: {result['error']}")


def example_document_analysis():
    """文档分析示例"""
    print("\n=== 文档分析示例 ===")
    
    ocr = OpenAIOCR()
    
    # 文档结构分析提示词
    document_prompt = """
    请分析这张文档图片，并提供以下信息：
    
    1. 文档类型（如：试卷、报告、表单、证书等）
    2. 主要内容摘要
    3. 文档结构分析（标题、段落、列表、表格等）
    4. 关键信息提取（日期、姓名、数字、重要术语等）
    5. 完整的文本内容（按阅读顺序）
    
    输出格式：
    ## 文档分析报告
    
    **文档类型**: [类型]
    
    **内容摘要**: [摘要]
    
    **结构分析**: [结构]
    
    **关键信息**: [关键信息]
    
    **完整内容**:
    [完整文本内容]
    """
    
    result = ocr.recognize_image("5.jpg", custom_prompt=document_prompt)
    
    if result['success']:
        print("文档分析结果:")
        print(result['text'])
    else:
        print(f"识别失败: {result['error']}")


def example_multilingual():
    """多语言识别示例"""
    print("\n=== 多语言识别示例 ===")
    
    ocr = OpenAIOCR()
    
    # 多语言识别提示词
    multilingual_prompt = """
    请分析这张图片中的文本内容，要求：
    
    1. 自动识别文本的主要语言
    2. 准确提取所有文本内容
    3. 保持原有的格式和结构
    4. 如果有多种语言混合，请分别标注
    5. 对于非拉丁字符（如中文、日文、阿拉伯文等），请特别注意准确性
    
    输出格式：
    **检测到的语言**: [语言列表]
    
    **提取的文本**:
    [按原格式输出的文本内容]
    
    **备注**: [如有特殊情况请说明]
    """
    
    result = ocr.recognize_image("5.jpg", custom_prompt=multilingual_prompt)
    
    if result['success']:
        print("多语言识别结果:")
        print(result['text'])
    else:
        print(f"识别失败: {result['error']}")


def example_batch_processing():
    """批量处理示例"""
    print("\n=== 批量处理示例 ===")
    
    ocr = OpenAIOCR()
    
    # 假设有多张图片需要处理
    image_files = ["5.jpg"]  # 可以添加更多图片路径
    
    # 批量处理的提示词
    batch_prompt = """
    请提取这张图片中的所有文本内容，要求：
    1. 按自然阅读顺序提取
    2. 保持原有格式
    3. 表格用HTML格式
    4. 标题用Markdown格式
    
    请直接输出提取的内容：
    """
    
    results = ocr.batch_recognize(image_files, custom_prompt=batch_prompt)
    
    for i, result in enumerate(results):
        print(f"\n--- 图片 {i+1} 处理结果 ---")
        if result['success']:
            print(f"文件: {result['image_path']}")
            print(f"内容:\n{result['text']}")
        else:
            print(f"处理失败: {result['error']}")


def main():
    """主函数 - 运行所有示例"""
    print("OpenAI OCR脚本使用示例")
    print("=" * 50)
    
    # 检查API密钥
    if not os.getenv('OPENAI_API_KEY'):
        print("警告: 未设置OPENAI_API_KEY环境变量")
        print("请先设置API密钥: export OPENAI_API_KEY='your-api-key'")
        return
    
    # 检查示例图片
    if not os.path.exists("5.jpg"):
        print("警告: 示例图片 5.jpg 不存在")
        print("请确保当前目录下有测试图片")
        return
    
    try:
        # 运行各种示例
        example_basic_usage()
        example_custom_prompts()
        example_table_extraction()
        example_document_analysis()
        example_multilingual()
        example_batch_processing()
        
    except Exception as e:
        print(f"示例运行出错: {str(e)}")


if __name__ == "__main__":
    main()
